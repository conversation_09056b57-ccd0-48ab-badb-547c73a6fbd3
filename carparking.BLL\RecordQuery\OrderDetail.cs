﻿using carparking.Common;
using carparking.Model;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using carparking.BLL.Cache;

namespace carparking.BLL
{
    public class OrderDetail : BaseBLL
    {
        static DAL.OrderDetail dal = new DAL.OrderDetail();

        public static List<string> NewOrderNo(string carno, int count = 1)
        {
            var noList = Utils.GetRandomLst(count);

            for (int i = 0; i < noList.Count; i++)
            {
                if (string.IsNullOrWhiteSpace(carno))
                    continue;

                if (carno.Length <= 6)
                    noList[i] = $"{noList[i]}-{carno}";
                if (carno.Length > 6)
                    noList[i] = $"{noList[i]}-{carno.Substring(carno.Length - 6)}";
            }

            return noList;
        }

        public static string NewOrderNo(string carno, string rand)
        {
            if (string.IsNullOrWhiteSpace(carno))
                return rand;
            if (carno.Length <= 6)
                return $"{rand} -{carno}";
            else
                return $"{rand}-{carno.Substring(carno.Length - 6)}";
        }

        public static Model.OrderDetail GetEntity(string OrderDetail_No)
        {
            return _GetEntityByWhere(new Model.OrderDetail(), "*", $"OrderDetail_No='{OrderDetail_No}'");
        }

        public static Model.OrderDetail GetEntity(string showFileds, string selectWhere)
        {
            return _GetEntityByWhere(new Model.OrderDetail(), showFileds, selectWhere);
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static Model.OrderDetailExt GetExtEntity(string showFields, string selectWhere)
        {
            return dal.GetExtEntity(showFields, selectWhere);
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OrderDetailExt> GetAllEntityExt(string showFields, string selectWhere)
        {
            return dal.GetAllExtEntity(showFields, selectWhere);
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OrderDetailExt> GetAllExtEntityGroupBy(string sparkNo)
        {
            return dal.GetAllExtEntityGroupBy(sparkNo);
        }

        /// <summary>
        /// 获取车辆在场记录
        /// </summary>
        /// <param name="parkNo"></param>
        /// <param name="carNo"></param>
        /// <returns></returns>
        public static Model.OrderDetail GetEntityByIn(string parkNo, string carNo)
        {
            StringBuilder sqlstr = new StringBuilder();
            sqlstr.Append($" OrderDetail_ParkNo='{parkNo}' ");
            sqlstr.Append($" AND OrderDetail_CarNo='{carNo}' ");
            sqlstr.Append($" AND OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}' ");

            Model.OrderDetail model = BLL.OrderDetail._GetEntityByWhere(new Model.OrderDetail(), "*", sqlstr.ToString());

            return model;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OrderDetail> GetAllEntity(string showFields, string selectWhere, object parameters = null)
        {
            return dal._GetAllEntity(new Model.OrderDetail(), showFields, selectWhere, parameters);
        }

        ///// <summary>
        ///// 获取少量实体
        ///// </summary>
        ///// <param name="showFields">字段</param>
        ///// <param name="selectWhere">查询条件</param>
        ///// <returns></returns>
        //public static List<Model.OrderDetail> GetAllEntity(string showFields, string selectWhere, int? dataType = 0, DateTime? time = default, int commtimeout = 0, object parameters = null)
        //{
        //    var tablename = "OrderDetail";
        //    if (dataType == 1)
        //    {
        //        if (time == default || time == null) time = DateTime.Now;
        //        tablename = $"OrderDetail_{time.Value.ToString("yyyy")}";
        //        var exist = CommonBLL.ExistDBTable(tablename);
        //        if (!exist) { return new List<Model.OrderDetail>(); }
        //    }

        //    return dal.GetAllEntity(showFields, selectWhere, tablename, commtimeout, parameters);
        //}



        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OrderDetailExt> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, string sOrderName = "")
        {
            return dal.GetExtList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord, sOrderName);
        }

        /// <summary>
        /// 获取停车订单关联的明细
        /// </summary>
        /// <param name="ParkOrder_No"></param>
        /// <returns></returns>
        public static List<Model.OrderDetail> GetAllEntity(string ParkOrder_No, int? dataType = 0, DateTime? time = default)
        {
            if (dataType == 1)
            {
                var tablename = "OrderDetail";
                if (time == default || time == null) time = DateTime.Now;
                tablename = $"OrderDetail_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return new List<Model.OrderDetail>(); }
                return dal.GetAllEntity("*", $"OrderDetail_ParkOrderNo=@ParkOrder_No", tablename, parameters: new { ParkOrder_No = ParkOrder_No });
            }

            return _GetAllEntity(new Model.OrderDetail(), "*", $"OrderDetail_ParkOrderNo=@ParkOrder_No", parameters: new { ParkOrder_No = ParkOrder_No });
        }

        public static List<Model.OrderDetail> GetAllList(string showFields, string selectWhere)
        {
            return dal.GetAllList(showFields, selectWhere);
        }
        /// <summary>
        /// 返回内场区域订单明细
        /// </summary>
        public static Model.OrderDetail GetOrderDetail(string showFields, string OrderDetail_ParkAreaNo, int? OrderDetail_StatusNo = 200)
        {
            return _GetEntityByWhere(new Model.OrderDetail(), showFields, $"OrderDetail_ParkAreaNo='{OrderDetail_ParkAreaNo}' AND OrderDetail_StatusNo='{OrderDetail_StatusNo}'");
        }


        /// <summary>
        /// 获取停车订单详情
        /// </summary>
        public static List<Model.OrderDetail> GetOrderDetailList(string showFields, string OrderDetail_ParkNo, string OrderDetail_ParkOrderNo, int? OrderDetail_IsSettle)
        {
            List<Model.OrderDetail> OrderDetailList = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), showFields, $"OrderDetail_ParkNo='{OrderDetail_ParkNo}' and OrderDetail_ParkOrderNo='{OrderDetail_ParkOrderNo}' and OrderDetail_IsSettle={OrderDetail_IsSettle}");
            return OrderDetailList;
        }


        /// <summary>
        /// 创建入场明细
        /// </summary>
        public static Model.OrderDetail CreateOrderDetail(string ParkOrder_No, string Parking_No, string ParkArea_No, string ParkArea_Name, string Car_No, string CarCardType_No, string CarCardType_Name, string CarType_No, string CarType_Name, DateTime EnterTime, string Passway_No, string Passway_Name, string admin = "", string adminName = "", string detailNo = "")
        {
            string OrderDetail_No = string.IsNullOrEmpty(detailNo) ? $"{Utils.CreateNumberWith()}-{(Car_No.Length > 6 ? Car_No.Substring(Car_No.Length - 6, 6) : "")}" : detailNo;
            Model.OrderDetail parkOrder = new Model.OrderDetail()
            {
                OrderDetail_No = OrderDetail_No,
                OrderDetail_ParkOrderNo = ParkOrder_No,
                OrderDetail_ParkNo = Parking_No,
                OrderDetail_ParkAreaNo = ParkArea_No,
                OrderDetail_ParkAreaName = ParkArea_Name,
                OrderDetail_CarNo = Car_No,
                OrderDetail_CarCardType = CarCardType_No,
                OrderDetail_CarCardTypeName = CarCardType_Name,
                OrderDetail_CarType = CarType_No,
                OrderDetail_CarTypeName = CarType_Name,
                OrderDetail_StatusNo = 199,
                OrderDetail_EnterTime = EnterTime,
                OrderDetail_EnterPasswayNo = Passway_No,
                OrderDetail_EnterPasswayName = Passway_Name,
                OrderDetail_EnterAdminAccount = admin,
                OrderDetail_EnterAdminName = adminName,
                OrderDetail_EnterImgPath = "",
                OrderDetail_OutType = 0,
                OrderDetail_TotalAmount = 0,
                OrderDetail_Lock = 0,
                OrderDetail_UserNo = "",
                OrderDetail_QrCodeType = 1,
                OrderDetail_Remark = "",
                OrderDetail_IsSettle = 0
            };

            return parkOrder;
        }

        public static bool UpdateByList(List<Model.OrderDetail> odList)
        {
            return dal.UpdateByList(odList);
        }

        /// <summary>
        /// 更新订单
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="detaiList">停车明细</param>
        /// <param name="payModelList">支付订单</param>
        /// <param name="couponPayList">优惠券</param>
        /// <param name="carList">车辆信息</param>
        /// <param name="ownerList">车主信息</param>
        /// <param name="payPartList">支付明细</param>
        /// <returns></returns>
        public static bool UpdateByList(List<Model.ParkOrder> parkOrderList = null, List<Model.OrderDetail> detaiList = null, List<Model.PayOrder> payModelList = null, List<Model.CouponRecord> couponPayList = null, List<Model.Car> carList = null, List<Model.Owner> ownerList = null, List<Model.PayPart> payPartList = null, List<Model.Reserve> reserves = null, List<Model.Paymethod> paymethods = null,
            List<Model.DetentionPenalty> penaltyList = null, Model.ControlEvent cev = null, bool isUpdateIncar = true, List<Model.Ledger> ledgerList = null, List<Model.HoursTotal> hoursToals = null,
            List<Model.CarFees> carfees = null, List<Model.CarFeesOrder> carFeesOrders = null, List<Model.OverdueBill> obills = null)
        {
            return UpdateByList2(parkOrderList, detaiList, payModelList, couponPayList, carList, ownerList, payPartList, reserves, paymethods, penaltyList, cev != null ? new List<Model.ControlEvent>() { cev } : null, isUpdateIncar, ledgerList, hoursToals, carfees: carfees, feeOrders: carFeesOrders, obills: obills);
        }



        /// <summary>
        /// 更新订单
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="detaiList">停车明细</param>
        /// <param name="payModelList">支付订单</param>
        /// <param name="couponPayList">优惠券</param>
        /// <param name="carList">车辆信息</param>
        /// <param name="ownerList">车主信息</param>
        /// <param name="payPartList">支付明细</param>
        /// <returns></returns>
        public static bool UpdateByList2(List<Model.ParkOrder> parkOrderList = null, List<Model.OrderDetail> detaiList = null, List<Model.PayOrder> payModelList = null, List<Model.CouponRecord> couponPayList = null, List<Model.Car> carList = null, List<Model.Owner> ownerList = null, List<Model.PayPart> payPartList = null, List<Model.Reserve> reserves = null, List<Model.Paymethod> paymethods = null, List<Model.DetentionPenalty> penaltyList = null, List<Model.ControlEvent> cevList = null, bool isUpdateIncar = true, List<Model.Ledger> ledgerList = null, List<Model.HoursTotal> hoursToals = null,
            List<Model.CarFees> carfees = null, List<Model.CarFeesOrder> feeOrders = null, List<Model.OverdueBill> obills = null)
        {
            //更新欠费订单时，查询场内订单
            if (isUpdateIncar && parkOrderList != null && parkOrderList.Count > 0)
            {
                var po = parkOrderList.Where(x => x != null).OrderBy(x => x.ParkOrder_EnterTime).ToList().LastOrDefault();
                var inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", "InCar_CarNo=@InCar_CarNo", new { InCar_CarNo = po.ParkOrder_CarNo });
                if (inModel != null)
                {
                    if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
                    {
                        if (inModel.InCar_Status > EnumParkOrderStatus.In)//同一订单，入场状态大于当前订单状态，不更新Incar状态
                        {
                            isUpdateIncar = false;
                            po.ParkOrder_StatusNo = inModel.InCar_Status;
                        }
                    }
                    else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime && inModel.InCar_Status > EnumParkOrderStatus.Est)//入场时间小于当前入场记录时间，不更新Incar状态
                    {
                        isUpdateIncar = false;
                        if (po.ParkOrder_StatusNo < EnumParkOrderStatus.Out) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                    }
                }
            }

            var result = dal.UpdateByList2(parkOrderList, detaiList, payModelList, couponPayList, carList, ownerList, payPartList, reserves, paymethods, penaltyList, cevList, isUpdateIncar, ledgerList, hoursToals, carfees, feeOrders, obills);
            if (result && AppBasicCache.ReadWriteCache)
            {
                carList?.ForEach(item => { if (item != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, item.Car_CarNo, item); } });
                ownerList?.ForEach(item => { if (item != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, item.Owner_No, item); } });
                reserves?.ForEach(item => { if (item != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, item.Reserve_No, item); } });
            }
            return result;
        }

        /// <summary>
        /// 订单出场，更新关联数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="isForward">是否属于其它岗亭转发过来的数据</param>
        /// <returns></returns>
        public static bool UpdateByList(Model.ResBodyDataOut data, bool isForward = false)
        {
            //var incarUpdate = true;
            //if (isForward && data?.Item1?.Count > 0)
            //{
            //    var po = data.Item1.Where(x => x != null).OrderBy(x => x.ParkOrder_EnterTime).ToList().LastOrDefault();
            //    Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", $"InCar_CarNo='{po.ParkOrder_CarNo}'");
            //    if (inModel != null)
            //    {
            //        if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
            //        {
            //            if (inModel.InCar_Status > EnumParkOrderStatus.In)//同一订单，入场状态大于当前订单状态，不更新Incar状态
            //            {
            //                incarUpdate = false;
            //                po.ParkOrder_StatusNo = inModel.InCar_Status;
            //            }
            //        }
            //        else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime)//入场时间小于当前入场记录时间，不更新Incar状态
            //        {
            //            incarUpdate = false;
            //            if (po.ParkOrder_StatusNo < EnumParkOrderStatus.Out) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
            //        }
            //    }
            //}

            var result = UpdateByList(data.Item1, data.Item2, data.Item3, data.Item4, data.Item5, data.Item6, data.Item7, data.Item8, data.Item10, data.Item11, ledgerList: data.ledgerList, hoursToals: data.Item12, carfees: data.Item13, carFeesOrders: data.Item14, obills: data.Item15);
            return result;
        }

        /// <summary>
        /// 删除车辆预入场订单
        /// </summary>
        /// <param name="Parking_No"></param>
        /// <param name="Car_No"></param>
        /// <returns></returns>
        public static int DeleteByEst(string Parking_No, string Car_No)
        {
            return dal.DeleteByEst(Parking_No, Car_No);
        }


        /// <summary>
        /// 自定义条件分页,获得实体列表(用于查询外部数据库)
        /// </summary>
        public static List<Model.OrderDetail> GetAllEntity(string showFields, string selectWhere, System.Data.IDbConnection dbConnection)
        {
            return dal.GetAllEntity(showFields, selectWhere, dbConnection);
        }

        #region 获取车辆场内订单明细 & 所在区域
        /// <summary>
        /// 获取车辆场内订单明细 & 所在区域
        /// </summary>
        public static void GetCurrentOrderCar(string parkNo, string carNo, out (Model.OrderDetail, Model.ParkArea) data)
        {
            var detail = GetEntityByIn(parkNo, carNo);

            GetCurrentOrderCar(detail, out data);
        }

        /// <summary>
        /// 获取车辆场内订单明细 & 所在区域
        /// </summary>
        public static void GetCurrentOrderCar(Model.OrderDetail detail, out (Model.OrderDetail, Model.ParkArea) data)
        {
            data = (null, null);
            if (detail != null)
                data.Item1 = detail;
            if (detail != null)
                data.Item2 = BLL.ParkArea.GetEntity(detail.OrderDetail_ParkOrderNo);
        }
        #endregion

        /// <summary>
        /// 获取区域内场内车辆数
        /// </summary>
        /// <param name="areaNo"></param>
        /// <param name="disTime">临时车超期释放车位时间节点</param>
        /// <param name="carFree">免费车不占车位数</param>
        /// <param name="cardnos">统计临时车/固定车</param>
        /// <returns></returns>
        public static int GetCountInPark(string areaNo, DateTime? disTime = null, List<string> carFree = null, List<string> cardnos = null)
        {
            return dal.GetCountInPark(areaNo, disTime, carFree, cardnos);
        }
    }
}
