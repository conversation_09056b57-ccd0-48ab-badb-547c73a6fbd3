using carparking.BLL.Cache;
using carparking.Common;
using carparking.DAL;
using carparking.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;

namespace carparking.BLL
{
    public class RptAnalysis : BaseBLL
    {
        static DAL.RptAnalysis dal = new DAL.RptAnalysis();

        /// <summary>
        /// 校验时间范围是否合法
        /// </summary>
        /// <param name="t1">开始时间object</param>
        /// <param name="t2">截止时间object</param>
        /// <param name="msg">错误消息</param>
        /// <param name="t3">返回开始时间</param>
        /// <param name="t4">返回结束时间</param>
        /// <returns></returns>
        public static bool ChkDate(object t1, object t2, ref DateTime? t3, ref DateTime? t4)
        {
            DateTime start, end;
            if (t1 == null ||
                t2 == null ||
                !DateTime.TryParse(t1.ToString(), out start) ||
                !DateTime.TryParse(t2.ToString(), out end))
            {
                return false;
            }

            t3 = start;
            t4 = end;
            if (t3 > t4)
                return false;

            return true;
        }

        public static bool ChkDate(object t1, ref DateTime? t2)
        {
            DateTime start;
            if (t1 == null || !DateTime.TryParse(t1.ToString(), out start))
            {
                return false;
            }
            t2 = start;
            return true;
        }

        /// <summary>
        /// 获取支付订单图表数据
        /// </summary>
        public static List<Model.RptPayOrder> GetAllEntity(string showFileds, string selectWhere, int dataType = 0, DateTime? time = default)
        {
            var tablename = "PayOrder";
            if (dataType == 1)
            {
                if (time == null || time == default) time = DateTime.Now;
                tablename = $"PayOrder_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return new List<RptPayOrder>(); }
            }

            return dal.GetAllEntity(showFileds, selectWhere, tablename);
        }

        /// <summary>
        /// 获取车流量图表数据
        /// </summary>
        public static List<Model.RptTraffic> GetAllTraffic(string showFileds, string selectWhere, int dataType = 0, DateTime? time = default)
        {
            var tablename = "ParkOrder";
            if (dataType == 1)
            {
                if (time == null || time == default) time = DateTime.Now;
                tablename = $"ParkOrder_{time.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return new List<Model.RptTraffic>(); }
            }

            return dal.GetAllTraffic(showFileds, selectWhere, tablename);
        }

        /// <summary>
        /// 获取支付类型分析数据
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="orders">图表数据</param>
        /// <returns></returns>
        public static Model.RptPayCodeType GetRptAnalysisPayCodeType(DateTime? start, DateTime? end, List<Model.RptPayOrder> orders)
        {
            List<Model.RptAnalysisSqlModel> total = new List<Model.RptAnalysisSqlModel>();      //全部
            List<Model.RptAnalysisSqlModel> ptcoast = new List<Model.RptAnalysisSqlModel>();    //平台现金支付
            List<Model.RptAnalysisSqlModel> xxcoast = new List<Model.RptAnalysisSqlModel>();    //线下现金支付
            List<Model.RptAnalysisSqlModel> alipay = new List<Model.RptAnalysisSqlModel>();     //支付宝支付
            List<Model.RptAnalysisSqlModel> wxpay = new List<Model.RptAnalysisSqlModel>();      //微信支付
            List<Model.RptAnalysisSqlModel> other = new List<Model.RptAnalysisSqlModel>();      //其他支付

            List<string> ptcoastCode = new List<string>() { Model.EnumPayType.Platform.ToString() };
            List<string> xxcoastCode = new List<string>() { Model.EnumPayType.OffLineCash.ToString() };
            List<string> alipayCode = new List<string>() { Model.EnumPayType.AliPay.ToString() };
            List<string> wxpayCode = new List<string>() { Model.EnumPayType.WeiChat.ToString() };
            List<string> allcode = new List<string>();
            allcode.AddRange(ptcoastCode);
            allcode.AddRange(xxcoastCode);
            allcode.AddRange(alipayCode);
            allcode.AddRange(wxpayCode);

            while (start <= end)
            {
                DateTime dayEnd = start.Value.AddDays(1);



                var dayOrderList = orders.FindAll(x => x.PayOrder_PayedTime >= start && x.PayOrder_PayedTime < dayEnd);

                var totalNum = dayOrderList.Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModeltotal = new Model.RptAnalysisSqlModel();
                dayModeltotal.date = start;
                dayModeltotal.num = totalNum.Value.ToString("f2");
                total.Add(dayModeltotal);

                var ptcoastNum = dayOrderList.FindAll(x => ptcoastCode.Contains(x.PayOrder_PayTypeCode)).Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModelptcoast = new Model.RptAnalysisSqlModel();
                dayModelptcoast.date = start;
                dayModelptcoast.num = ptcoastNum.Value.ToString("f2");
                ptcoast.Add(dayModelptcoast);

                var xxcoastNum = dayOrderList.FindAll(x => xxcoastCode.Contains(x.PayOrder_PayTypeCode)).Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModelxxcoast = new Model.RptAnalysisSqlModel();
                dayModelxxcoast.date = start;
                dayModelxxcoast.num = xxcoastNum.Value.ToString("f2");
                xxcoast.Add(dayModelxxcoast);

                var alipayNum = dayOrderList.FindAll(x => alipayCode.Contains(x.PayOrder_PayTypeCode)).Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModelalipay = new Model.RptAnalysisSqlModel();
                dayModelalipay.date = start;
                dayModelalipay.num = alipayNum.Value.ToString("f2");
                alipay.Add(dayModelalipay);

                var wxpayNum = dayOrderList.FindAll(x => wxpayCode.Contains(x.PayOrder_PayTypeCode)).Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModelwxpay = new Model.RptAnalysisSqlModel();
                dayModelwxpay.date = start;
                dayModelwxpay.num = wxpayNum.Value.ToString("f2");
                wxpay.Add(dayModelwxpay);

                var otherNum = dayOrderList.FindAll(x => !allcode.Contains(x.PayOrder_PayTypeCode)).Sum(x => x.PayOrder_PayedMoney);
                Model.RptAnalysisSqlModel dayModelother = new Model.RptAnalysisSqlModel();
                dayModelother.date = start;
                dayModelother.num = otherNum.Value.ToString("f2");
                other.Add(dayModelother);

                start = dayEnd;
            }

            return new Model.RptPayCodeType() { alipay = alipay, other = other, ptcoast = ptcoast, total = total, wxpay = wxpay, xxcoast = xxcoast };
        }

        /// <summary>
        /// 动态获取支付类型分析数据
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="orders">图表数据</param>
        /// <returns></returns>
        public static List<Model.RptAnalysisAoto> GetRptAnalysisByPayCodeType(DateTime? start, DateTime? end, List<Model.RptPayOrder> orders)
        {
            var grouplist = orders.OrderBy(x => x.PayOrder_PayTypeCode).GroupBy(x => x.PayOrder_PayTypeCode).Select(group => new Model.RptAnalysisAoto()
            {
                typeCode = group.Key,
                typeName = Model.EnumPayType.GetPayTypeName(group.Key),
                orders = group.ToList(),
                total = group.ToList().Sum(a => a.PayOrder_PayedMoney).Value.ToString("f2"),
                rpts = new List<Model.RptAnalysisSqlModel>()
            }).ToList();

            while (start <= end)
            {
                DateTime dayEnd = start.Value.AddDays(1);

                for (int i = 0; i < grouplist.Count; i++)
                {
                    var total = grouplist[i].orders.FindAll(x => x.PayOrder_PayedTime >= start && x.PayOrder_PayedTime < dayEnd).Sum(x => x.PayOrder_PayedMoney);
                    Model.RptAnalysisSqlModel dayModel = new Model.RptAnalysisSqlModel();
                    dayModel.date = start;
                    dayModel.num = total.Value.ToString("f2");
                    grouplist[i].rpts.Add(dayModel);

                }
                start = dayEnd;
            }

            return grouplist;
        }

        /// <summary>
        /// 动态获取车流量分析数据
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <param name="orders">图表数据</param>
        /// <returns></returns>
        public static List<Model.RptAnalysisTrafficAoto> GetRptAnalysisByTraffic(DateTime? start, DateTime? end, List<Model.RptTraffic> inOrders, List<Model.RptTraffic> outOrders)
        {
            var grouplist = new List<Model.RptAnalysisTrafficAoto>();
            grouplist.Add(new Model.RptAnalysisTrafficAoto()
            {
                typeCode = "0",
                typeName = "入车流量",
                orders = inOrders,
                total = inOrders.Count().ToString(),
                rpts = new List<Model.RptAnalysisSqlModel>()
            });
            grouplist.Add(new Model.RptAnalysisTrafficAoto()
            {
                typeCode = "1",
                typeName = "出车流量",
                orders = outOrders,
                total = outOrders.Count().ToString(),
                rpts = new List<Model.RptAnalysisSqlModel>()
            });

            while (start <= end)
            {
                DateTime dayEnd = start.Value.AddDays(1);

                for (int i = 0; i < grouplist.Count; i++)
                {
                    int total = 0;
                    if (grouplist[i].typeCode == "0")
                        total = inOrders.FindAll(x => x.ParkOrder_EnterTime >= start && x.ParkOrder_EnterTime < dayEnd).Count();
                    else
                        total = outOrders.FindAll(x => x.ParkOrder_OutTime >= start && x.ParkOrder_OutTime < dayEnd).Count();

                    Model.RptAnalysisSqlModel dayModel = new Model.RptAnalysisSqlModel();
                    dayModel.date = start;
                    dayModel.num = total.ToString();
                    grouplist[i].rpts.Add(dayModel);
                }
                start = dayEnd;
            }

            return grouplist;
        }

        /// <summary>
        /// 返回收费统计展示数据【支付代码分析】
        /// </summary>
        public static Model.RptAnalysis GetRptAnalysis(DateTime? start, DateTime? end, List<Model.RptAnalysisSqlModel> models)
        {
            Model.RptAnalysis data = new Model.RptAnalysis();
            data.Data = models.ToArray();
            data.start = start;
            data.end = end;

            List<string> xData = new List<string>();
            List<string> yData = new List<string>();

            models.ForEach(x =>
            {
                xData.Add(x.name);
                yData.Add(x.num);
            });

            data.xData = xData.ToArray();
            data.yData = yData.ToArray();

            return data;
        }

        #region 临停收费统计

        /// <summary>
        /// 返回临停收费统计数据库返回数据
        /// </summary>
        public static List<Model.RptAnalysisAoto> TempRptAnalysis(DateTime? start, DateTime? end, int dataType = 0)
        {
            StringBuilder selectWhere = new StringBuilder();

            string OrderTypeNo = Model.EnumOrderType.Temp.ToString();   //临停缴费类型
            string MonthCharge = ((int)Common.EnumOrderType.MonthCharge).ToString();   //临停缴费类型
            selectWhere.Append($" PayOrder_OrderTypeNo in ('{OrderTypeNo}','{MonthCharge}') ");
            selectWhere.Append($" AND PayOrder_Status=1 ");
            selectWhere.Append($" AND PayOrder_PayedTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND PayOrder_PayedTime<'{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.RptPayOrder> orders = GetAllEntity("*", selectWhere.ToString(), dataType, start);

            return GetRptAnalysisByPayCodeType(start, end, orders);
        }

        /// <summary>
        /// 返回临停收费统计数据库返回数据
        /// </summary>
        public static List<Model.RptAnalysisTrafficAoto> TrafficRptAnalysis(DateTime? start, DateTime? end, int dataType = 0)
        {
            List<Model.RptTraffic> inOrders = GetAllTraffic("*", $"ParkOrder_EnterTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND ParkOrder_EnterTime< '{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}'", dataType, start);
            List<Model.RptTraffic> outOrders = GetAllTraffic("*", $"ParkOrder_OutTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND ParkOrder_OutTime < '{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' and ParkOrder_StatusNo in( {Model.EnumParkOrderStatus.Follow},{Model.EnumParkOrderStatus.In},{Model.EnumParkOrderStatus.Out})", dataType, start);
            return GetRptAnalysisByTraffic(start, end, inOrders, outOrders);
        }

        #endregion

        #region 月租收费统计

        /// <summary>
        /// 返回月租收费统计数据库返回数据
        /// </summary>
        public static List<Model.RptAnalysisAoto> MonthRptAnalysis(DateTime? start, DateTime? end, int dataType = 0)
        {
            StringBuilder selectWhere = new StringBuilder();

            string OrderTypeNo = Model.EnumOrderType.Month.ToString();   //月租缴费类型
            selectWhere.Append($" PayOrder_OrderTypeNo='{OrderTypeNo}' ");
            selectWhere.Append($" AND PayOrder_Status=1 ");
            selectWhere.Append($" AND PayOrder_PayedTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND PayOrder_PayedTime< '{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.RptPayOrder> orders = GetAllEntity("*", selectWhere.ToString(), dataType, start);

            return GetRptAnalysisByPayCodeType(start, end, orders);
        }

        #endregion

        #region 储值车缴费统计

        /// <summary>
        /// 返回储值车缴费统计数据库返回数据
        /// </summary>
        public static List<Model.RptAnalysisAoto> CarStoreAnalysis(DateTime? start, DateTime? end, int dataType = 0)
        {
            StringBuilder selectWhere = new StringBuilder();

            string OrderTypeNo = Model.EnumOrderType.Wallet.ToString();   //储值缴费类型
            selectWhere.Append($" PayOrder_OrderTypeNo='{OrderTypeNo}' ");
            selectWhere.Append($" AND PayOrder_Status=1 ");
            selectWhere.Append($" AND PayOrder_PayedTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND PayOrder_PayedTime< '{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.RptPayOrder> orders = GetAllEntity("*", selectWhere.ToString(), dataType, start);

            return GetRptAnalysisByPayCodeType(start, end, orders);
        }

        #endregion

        #region 车位续期统计

        /// <summary>
        /// 返回车位续期统计数据库返回数据
        /// </summary>
        public static List<Model.RptAnalysisAoto> SpaceRptAnalysis(DateTime? start, DateTime? end, int dataType = 0)
        {
            StringBuilder selectWhere = new StringBuilder();

            string OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();   //临停缴费类型
            selectWhere.Append($" PayOrder_OrderTypeNo='{OrderTypeNo}' ");
            selectWhere.Append($" AND PayOrder_Status=1 ");
            selectWhere.Append($" AND PayOrder_PayedTime >= '{start.Value.ToString("yyyy-MM-dd 00:00:00")}' AND PayOrder_PayedTime< '{end.Value.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.RptPayOrder> orders = GetAllEntity("*", selectWhere.ToString(), dataType, start);

            return GetRptAnalysisByPayCodeType(start, end, orders);
        }

        #endregion

        #region 日、月、年图表数据统计
        /// <summary>
        /// 返回日、月、年图表数据统计图表数据
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">截止时间[日、年时可以为空]</param>
        /// <param name="type">day,month,year</param>
        /// <returns></returns>
        public static List<Model.RptAnalysisAotoDate> GetDayOrMonthOrYearAnalysisAoto(DateTime? start, DateTime? end, string type, int dataType = 0)
        {
            var tablename = "payorder";
            if (dataType == 1)
            {
                if (start == default || start == null) start = DateTime.Now;
                tablename = $"payorder_{start.Value.ToString("yyyy")}";
                var exist = CommonBLL.ExistDBTable(tablename);
                if (!exist) { return new List<Model.RptAnalysisAotoDate>(); }
            }

            List<Model.RptAnalysisAotoDate> grouplist = new List<Model.RptAnalysisAotoDate>();
            if (type.Equals("day"))
            {
                var dataList = dal.GetDayData(start.Value, tablename);

                grouplist = dataList?.OrderBy(x => x.code)?.GroupBy(x => x.code).Select(group => new Model.RptAnalysisAotoDate()
                {
                    typeCode = group.Key,
                    typeName = Model.EnumPayType.GetPayTypeName(group.Key),
                    orders = group.ToList(),
                    total = group.ToList().Sum(a => a.num).ToString("f2"),
                    rpts = new List<Model.RptAnalysisSqlModelAoto>()
                }).ToList();

                List<string> xData = new List<string>();
                int i = 0;
                while (i < 24)
                {
                    xData.Add(i.ToString());
                    i++;
                }
                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        for (int n = 0; n < grouplist.Count; n++)
                        {
                            var total = grouplist[n].orders.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0')).Sum(x => x.num);
                            Model.RptAnalysisSqlModelAoto dayModel = new Model.RptAnalysisSqlModelAoto();
                            dayModel.date = start.Value.ToString("yyyy-MM-dd") + " " + xData[h];
                            dayModel.num = total.ToString("f2");
                            grouplist[n].rpts.Add(dayModel);

                        }
                    }
                }
            }
            else if (type.Equals("month"))
            {
                var dataList = dal.GetMonthData(start.Value, end.Value, tablename);

                List<string> xData = new List<string>();

                grouplist = dataList?.OrderBy(x => x.code)?.GroupBy(x => x.code).Select(group => new Model.RptAnalysisAotoDate()
                {
                    typeCode = group.Key,
                    typeName = Model.EnumPayType.GetPayTypeName(group.Key),
                    orders = group.ToList(),
                    total = group.ToList().Sum(a => a.num).ToString("f2"),
                    rpts = new List<Model.RptAnalysisSqlModelAoto>()
                }).ToList();

                while (start <= end)
                {
                    xData.Add(start.Value.ToString("yyyy-MM-dd"));
                    start = start.Value.AddDays(1);
                }


                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        for (int n = 0; n < grouplist.Count; n++)
                        {
                            var total = grouplist[n].orders.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h].ToString()).Sum(x => x.num);
                            Model.RptAnalysisSqlModelAoto dayModel = new Model.RptAnalysisSqlModelAoto();
                            dayModel.date = xData[h];
                            dayModel.num = total.ToString("f2");
                            grouplist[n].rpts.Add(dayModel);

                        }
                    }
                }
            }
            else if (type.Equals("year"))
            {
                var dataList = dal.GetYearData(start.Value, tablename);

                grouplist = dataList?.OrderBy(x => x.code)?.GroupBy(x => x.code).Select(group => new Model.RptAnalysisAotoDate()
                {
                    typeCode = group.Key,
                    typeName = Model.EnumPayType.GetPayTypeName(group.Key),
                    orders = group.ToList(),
                    total = group.ToList().Sum(a => a.num).ToString("f2"),
                    rpts = new List<Model.RptAnalysisSqlModelAoto>()
                }).ToList();

                List<string> xData = new List<string>();
                int i = 1;
                while (i <= 12)
                {
                    xData.Add(i.ToString());
                    i++;
                }
                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        for (int n = 0; n < grouplist.Count; n++)
                        {
                            var total = grouplist[n].orders.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0')).Sum(x => x.num);
                            Model.RptAnalysisSqlModelAoto dayModel = new Model.RptAnalysisSqlModelAoto();
                            dayModel.date = start.Value.Year + "-" + xData[h];
                            dayModel.num = total.ToString("f2");
                            grouplist[n].rpts.Add(dayModel);
                        }
                    }
                }
            }

            return grouplist;
        }
        /// <summary>
        /// 返回日、月、年图表数据统计图表数据
        /// </summary>
        /// <param name="start">开始时间</param>
        /// <param name="end">截止时间[日、年时可以为空]</param>
        /// <param name="type">day,month,year</param>
        /// <returns></returns>
        public static (Model.RptAnalysis all, Model.RptAnalysis pt, Model.RptAnalysis xx, Model.RptAnalysis alipay, Model.RptAnalysis wxpay, Model.RptAnalysis other) GetDayOrMonthOrYearAnalysis(DateTime? start, DateTime? end, string type)
        {
            (Model.RptAnalysis all, Model.RptAnalysis pt, Model.RptAnalysis xx, Model.RptAnalysis alipay, Model.RptAnalysis wxpay, Model.RptAnalysis other) res = (null, null, null, null, null, null);
            res.all = new Model.RptAnalysis();
            res.pt = new Model.RptAnalysis();
            res.xx = new Model.RptAnalysis();
            res.alipay = new Model.RptAnalysis();
            res.wxpay = new Model.RptAnalysis();
            res.other = new Model.RptAnalysis();

            List<string> ptcoastCode = new List<string>() { Model.EnumPayType.Platform.ToString() };
            List<string> xxcoastCode = new List<string>() { Model.EnumPayType.OffLineCash.ToString() };
            List<string> alipayCode = new List<string>() { Model.EnumPayType.AliPay.ToString() };
            List<string> wxpayCode = new List<string>() { Model.EnumPayType.WeiChat.ToString() };
            List<string> allcode = new List<string>();
            allcode.AddRange(ptcoastCode);
            allcode.AddRange(xxcoastCode);
            allcode.AddRange(alipayCode);
            allcode.AddRange(wxpayCode);

            if (type.Equals("day"))
            {
                var dataList = dal.GetDayData(start.Value);

                res.all.yData = new string[24];
                res.pt.yData = new string[24];
                res.xx.yData = new string[24];
                res.alipay.yData = new string[24];
                res.wxpay.yData = new string[24];
                res.other.yData = new string[24];

                List<string> xData = new List<string>();
                int i = 0;
                while (i < 24)
                {
                    xData.Add(i.ToString());
                    i++;
                }
                res.all.xData = res.pt.xData = res.xx.xData = res.alipay.xData = res.wxpay.xData = res.other.xData = xData.ToArray();

                res.all.yData = new string[xData.Count];
                res.pt.yData = new string[xData.Count];
                res.xx.yData = new string[xData.Count];
                res.alipay.yData = new string[xData.Count];
                res.wxpay.yData = new string[xData.Count];
                res.other.yData = new string[xData.Count];
                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        res.all.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0')).Sum(x => x.num).ToString();
                        res.pt.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && ptcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.xx.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && xxcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.alipay.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && alipayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.wxpay.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && wxpayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.other.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && !allcode.Contains(x.code)).Sum(x => x.num).ToString();
                    }
                }
            }
            else if (type.Equals("month"))
            {
                var dataList = dal.GetMonthData(start.Value, end.Value);

                List<string> xData = new List<string>();

                while (start <= end)
                {
                    xData.Add(start.Value.ToString("yyyy-MM-dd"));
                    start = start.Value.AddDays(1);
                }
                res.all.xData = res.pt.xData = res.xx.xData = res.alipay.xData = res.wxpay.xData = res.other.xData = xData.ToArray();
                res.all.yData = new string[xData.Count];
                res.pt.yData = new string[xData.Count];
                res.xx.yData = new string[xData.Count];
                res.alipay.yData = new string[xData.Count];
                res.wxpay.yData = new string[xData.Count];
                res.other.yData = new string[xData.Count];

                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        res.all.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h]).Sum(x => x.num).ToString();
                        res.pt.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h] && ptcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.xx.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h] && xxcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.alipay.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h] && alipayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.wxpay.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h] && wxpayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.other.yData[h] = dataList.FindAll(x => x.day.Value.ToString("yyyy-MM-dd") == xData[h] && !allcode.Contains(x.code)).Sum(x => x.num).ToString();
                    }
                }
            }
            else if (type.Equals("year"))
            {
                var dataList = dal.GetYearData(start.Value);

                List<string> xData = new List<string>();
                int i = 1;
                while (i <= 12)
                {
                    xData.Add(i.ToString());
                    i++;
                }
                res.all.xData = res.pt.xData = res.xx.xData = res.alipay.xData = res.wxpay.xData = res.other.xData = xData.ToArray();
                res.all.yData = new string[xData.Count];
                res.pt.yData = new string[xData.Count];
                res.xx.yData = new string[xData.Count];
                res.alipay.yData = new string[xData.Count];
                res.wxpay.yData = new string[xData.Count];
                res.other.yData = new string[xData.Count];
                for (int h = 0; h < xData.Count; h++)
                {
                    if (dataList != null)
                    {
                        res.all.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0')).Sum(x => x.num).ToString();
                        res.pt.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && ptcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.xx.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && xxcoastCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.alipay.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && alipayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.wxpay.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && wxpayCode.Contains(x.code)).Sum(x => x.num).ToString();
                        res.other.yData[h] = dataList.FindAll(x => x.name.TrimStart('0') == xData[h].ToString().TrimStart('0') && !allcode.Contains(x.code)).Sum(x => x.num).ToString();
                    }
                }
            }

            return res;
        }
        #endregion


        /// <summary>
        /// 创建电子支付表格前文
        /// </summary>
        /// <param name="onlineData">电子支付数据集</param>
        /// <param name="colNum">每行列数</param>
        /// <returns></returns>
        public static string CreateOnlinePayTdHtml(Model.RptPrintOnline onlineData, int colNum)
        {
            List<Model.PayType> payTypes = BLL.BaseBLL._GetAllEntity(new Model.PayType(), "*", $"PayType_Enable=1");

            string html = string.Empty;
            List<string> trTitle = new List<string>();
            List<string> trValue = new List<string>();
            string th = "<th>{0}</th>";
            string td = "<td>{0}</td>";

            trTitle.Add(string.Format(th, "支付方式"));
            trValue.Add(string.Format(td, "合计"));

            PropertyInfo[] ps = typeof(Model.RptPrintOnline).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            for (int i = 0; i <= ps.Length - 1; i++)
            {

                List<object> objs = ps[i].GetCustomAttributes(typeof(DescriptionAttribute), true).ToList();
                string name = ((DescriptionAttribute)objs?.FirstOrDefault())?.Description;
                if (!string.IsNullOrWhiteSpace(name))
                {
                    var pt = payTypes?.Find(x => x.PayType_No == name);
                    if (pt != null)
                    {
                        trTitle.Add(string.Format(th, pt.PayType_Nickname));
                        trValue.Add(string.Format(td, ps[i].GetValue(onlineData) ?? 0));

                        if (pt.PayType_No == Model.EnumPayType.owinDeviceCashMoney.ToString())
                        {
                            trTitle.Add(string.Format(th, "找零金额"));
                            trValue.Add(string.Format(td, onlineData.ZhaoLing));
                        }
                    }
                }

                if (trTitle.Count >= colNum)
                {
                    html += $"<tr>{string.Join("", trTitle)}</tr>";
                    html += $"<tr>{string.Join("", trValue)}</tr>";

                    trTitle = new List<string>();
                    trValue = new List<string>();
                    trTitle.Add(string.Format(th, "支付方式"));
                    trValue.Add(string.Format(td, "合计"));
                }
                else if (i == ps.Length - 1)
                {
                    for (int h = trTitle.Count; h < colNum; h++)
                    {
                        trTitle.Add(string.Format(th, ""));
                        trValue.Add(string.Format(td, ""));
                    }

                    html += $"<tr>{string.Join("", trTitle)}</tr>";
                    html += $"<tr>{string.Join("", trValue)}</tr>";
                }
            }

            return html;
        }

        /// <summary>
        /// 获取日报打印数据
        /// </summary>
        /// <param name="groupType">分组类型:0-操作员,1-区域,2-操作员+区域</param>
        public static void GetRptPrintData(string parkno, DateTime start, DateTime end, out Model.RptPrintData data, int dataType = 0, int groupType = 0)
        {
            data = new Model.RptPrintData()
            {
                onlineData = new Model.RptPrintOnline(),
                RptPrintList = new List<Model.RptPrintItem>(),
                RptPrintListTotal = new Model.RptPrintItem()
            };

            List<Model.PayType> pts = BLL.BaseBLL._GetAllEntity(new Model.PayType(), "*", $"PayType_Enable=1");

            List<string> orderFileds = new List<string>()
            {
                "ParkOrder_No",
                "ParkOrder_CarCardType",
                "ParkOrder_CarType",
                "ParkOrder_StatusNo",
                "ParkOrder_IsFree",
                "ParkOrder_IsNoInRecord",
                "ParkOrder_CarCardType",
                "ParkOrder_OutAdminAccount",
                "ParkOrder_ParkAreaNo",
                "ParkOrder_ParkAreaName",
            };

            List<string> orderDetailFileds = new List<string>()
            {
                "OrderDetail_No",
                "OrderDetail_CarCardType",
                "OrderDetail_CarType",
                "OrderDetail_StatusNo",
                "OrderDetail_IsCharge",
                "OrderDetail_FreeReason",
                "OrderDetail_OutPasswayNo",
                "OrderDetail_OutAdminAccount",
                "OrderDetail_OutPasswayName",
                "OrderDetail_TotalAmount",
                "OrderDetail_ParkAreaNo",
                "OrderDetail_ParkAreaName",
            };

            StringBuilder orderSql = new StringBuilder();
            orderSql.Append($" ParkOrder_ParkNo='{parkno}' ");
            orderSql.Append($" AND ParkOrder_StatusNo=201 ");
            orderSql.Append($" AND ParkOrder_OutTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND ParkOrder_OutTime< '{end.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}' ");
            List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity(string.Join(",", orderFileds), orderSql.ToString(), dataType, start);

            var normalOrder = orders?.FindAll(x => x.ParkOrder_IsFree == 0);
            var freeOrder = orders?.FindAll(x => x.ParkOrder_IsFree == 1);
            var freeOrderNo = freeOrder?.Select(x => x.ParkOrder_No).ToList() ?? new List<string>();



            StringBuilder passRWhere = new StringBuilder();
            passRWhere.Append($" PassRecord_ParkNo='{parkno}' ");
            passRWhere.Append($" AND (PassRecord_Type=1 OR PassRecord_Type=2) ");
            passRWhere.Append($" AND PassRecord_PassTime  >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND PassRecord_PassTime< '{end.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}' ");
            List<Model.PassRecord> passRecordList = BLL.PassRecord.GetAllEntity("PassRecord_PasswayNo,PassRecord_Type,PassRecord_Account", passRWhere.ToString(), null) ?? new List<Model.PassRecord>();

            List<string> payFileds = new List<string>()
            {
                "PayOrder_OrderTypeNo",
                "PayOrder_Status",
                "PayOrder_PayedTime",
                "PayOrder_PayTypeCode",
                "PayOrder_Account",
                "PayOrder_Category",
                "PayOrder_CarCardTypeNo",
                "PayOrder_Money",
                "PayOrder_PayedMoney",
                "PayOrder_StoredMoney",
                "PayOrder_ParkOrderNo",
                "PayOrder_DiscountMoney",
                "PayOrder_PayedTime",
                "PayOrder_SelfMoney",
                "PayOrder_OutReduceMoney",
                "PayOrder_PassWayNo"
            };

            StringBuilder paySql = new StringBuilder();
            paySql.Append($" PayOrder_ParkNo='{parkno}' ");
            paySql.Append($" AND PayOrder_Status=1 ");
            paySql.Append($" AND (PayOrder_OrderTypeNo=5901 OR PayOrder_OrderTypeNo=5905 OR PayOrder_OrderTypeNo=5919)");//查询临时车缴费/月租车缴费/储值车缴费
            paySql.Append($" AND PayOrder_PayedTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND  PayOrder_PayedTime < '{end.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}' ");
            List<Model.PayOrder> payOrderList = BLL.PayOrder.GetAllEntity(string.Join(",", payFileds), paySql.ToString(), dataType, start);

            StringBuilder orderDetailSql = new StringBuilder();
            orderDetailSql.Append($" OrderDetail_ParkNo='{parkno}' ");
            orderDetailSql.Append($" AND OrderDetail_StatusNo=201 ");
            orderDetailSql.Append($" AND OrderDetail_OutTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND OrderDetail_OutTime< '{end.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}' ");
            List<Model.OrderDetail> orderdetails = BLL.OrderDetail.GetAllEntity(string.Join(",", orderDetailFileds), orderDetailSql.ToString());

            // 添加orderdetails的分类变量
            var normalOrderDetail = orderdetails?.FindAll(x => normalOrder?.Any(y => y.ParkOrder_No == x.OrderDetail_ParkOrderNo) ?? false); // 收费订单详情
            var freeOrderDetail = orderdetails?.FindAll(x => freeOrder?.Any(y => y.ParkOrder_No == x.OrderDetail_ParkOrderNo) ?? false);   // 免费订单详情

            //操作员不为空,线下现金
            var activePay = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineCash.ToString() && !string.IsNullOrWhiteSpace(x.PayOrder_Account));
            //不包含免费放行和支付订单,线下现金
            var normalPay = activePay?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineCash.ToString() && !freeOrderNo.Contains(x.PayOrder_ParkOrderNo));
            //线下现金支付订单
            var payOrderCashList = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineCash.ToString() && !string.IsNullOrWhiteSpace(x.PayOrder_Account));

            List<Model.PayPart> payPartList = null;
            if (groupType != 0 && payOrderList.Count > 0)
            {
                List<string> paypartFileds = new List<string>()
                {
                    "PayPart_PayOrderNo",
                    "PayPart_OrderTypeNo",
                    "PayPart_Status",
                    "PayPart_AddTime",
                    "PayPart_PayTypeCode",
                    "PayPart_Category",
                    "PayPart_CarCardTypeNo",
                    "PayPart_OrderMoney",
                    "PayPart_PayedMoney",
                    "PayPart_StoredMoney",
                    "PayPart_ParkOrderNo",
                    "PayPart_CouponMoney",
                    "PayPart_PasswayNo",
                    "PayPart_ParkAreaNo",
                    "PayPart_ParkAreaName"
                };
                StringBuilder paySql2 = new StringBuilder();
                paySql2.Append($" PayPart_ParkNo='{parkno}' ");
                paySql2.Append($" AND PayPart_Status=1 ");
                paySql2.Append($" AND (PayPart_OrderTypeNo=5901 OR PayPart_OrderTypeNo=5905 OR PayPart_OrderTypeNo=5919)");//查询临时车缴费/月租车缴费/储值车缴费
                paySql2.Append($" AND PayPart_AddTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND  PayPart_AddTime < '{end.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}' ");
                payPartList = BLL.PayPart.GetAllEntity(string.Join(",", paypartFileds), paySql2.ToString(), dataType, start);
                payPartList = payPartList.FindAll(x => payOrderList.Any(y => y.PayOrder_No == x.PayPart_PayOrderNo));
            }


            BLL.CarCardType.GetAllCardNoByType(parkno, out var temp, out var month, out var store, out var free, out var visit, out var bus);
            //访客车&商家车合并统计在临时车列
            if (visit != null) temp?.AddRange(visit);
            if (bus != null) temp?.AddRange(bus);

            //分组：key=区域编码，value=车道编码集合（PassRecord_PasswayNo属于车道编码，PayOrder_PassWayNo属于车道编码，ParkOrder_OutPasswayNo属于出口车道编码）
            var groupedResult = AppBasicCache.GetAllPasswayLink.Values
                               .GroupBy(p => p.PasswayLink_ParkAreaNo)
                               .ToDictionary(
                                   g => g.Key,
                                   g => g.GroupBy(x => x.PasswayLink_PasswayNo)
                                         .Select(pg =>
                                         {
                                             // 如果有两个（进出口），优先取出口
                                             var export = pg.FirstOrDefault(x => x.PasswayLink_GateType == 0);
                                             return export ?? pg.First(); // 如果没有出口，就取任意一个
                                         })
                                         .Select(x => x.PasswayLink_PasswayNo)
                                         .ToList()
                               );

            // 根据分组类型创建分组键列表
            List<string> groupKeys = new List<string>();

            switch (groupType)
            {
                case 0: // 操作员
                    List<string> passAccounts = passRecordList?.Select(x => x.PassRecord_Account).ToList() ?? new List<string>();
                    List<string> payAccounts = activePay?.Select(x => x.PayOrder_Account).ToList() ?? new List<string>();
                    List<string> opAccountList = orders?.Select(x => x.ParkOrder_OutAdminAccount).ToList() ?? new List<string>();
                    List<string> orderDetailAccounts = orderdetails?.Select(x => x.OrderDetail_OutAdminAccount).ToList() ?? new List<string>();
                    opAccountList?.AddRange(payAccounts);
                    opAccountList?.AddRange(passAccounts);
                    opAccountList?.AddRange(orderDetailAccounts);
                    groupKeys = opAccountList?.Distinct().Where(x => !string.IsNullOrEmpty(x)).ToList() ?? new List<string>();
                    break;

                case 1: // 区域
                    // 从groupedResult中获取所有区域编码，确保包含所有有车道的区域
                    var areaListFromGrouped = groupedResult.Keys.ToList();
                    // 从订单中获取区域编码作为补充
                    var areaListFromOrders = orders?.Select(x => x.ParkOrder_ParkAreaNo).Where(x => !string.IsNullOrEmpty(x)).ToList() ?? new List<string>();
                    // 从订单详情中获取区域编码作为补充
                    var areaListFromOrderDetails = orderdetails?.Select(x => x.OrderDetail_ParkAreaNo).Where(x => !string.IsNullOrEmpty(x)).ToList() ?? new List<string>();
                    // 合并并去重
                    groupKeys = areaListFromGrouped.Union(areaListFromOrders).Union(areaListFromOrderDetails).Distinct().Where(x => !string.IsNullOrEmpty(x)).ToList();
                    break;

                case 2: // 操作员+区域
                    // 从订单中获取实际存在的操作员-区域组合
                    var orderCombinations = orders?.Where(x => !string.IsNullOrEmpty(x.ParkOrder_OutAdminAccount) && !string.IsNullOrEmpty(x.ParkOrder_ParkAreaName))
                        .Select(x => $"{x.ParkOrder_OutAdminAccount}|{x.ParkOrder_ParkAreaName}").Distinct().ToList() ?? new List<string>();

                    // 从订单详情中获取操作员-区域组合
                    var orderDetailCombinations = orderdetails?.Where(x => !string.IsNullOrEmpty(x.OrderDetail_OutAdminAccount) && !string.IsNullOrEmpty(x.OrderDetail_ParkAreaName))
                        .Select(x => $"{x.OrderDetail_OutAdminAccount}|{x.OrderDetail_ParkAreaName}").Distinct().ToList() ?? new List<string>();

                    // 从支付记录中获取操作员-区域组合（通过车道编码关联区域）
                    var payAccountAreaCombinations = new List<string>();
                    if (activePay != null)
                    {
                        foreach (var pay in activePay.Where(x => !string.IsNullOrEmpty(x.PayOrder_Account) && !string.IsNullOrEmpty(x.PayOrder_PassWayNo)))
                        {
                            // 通过车道编码找到对应的区域
                            var areaNo = groupedResult.FirstOrDefault(g => g.Value.Contains(pay.PayOrder_PassWayNo)).Key;
                            if (!string.IsNullOrEmpty(areaNo))
                            {
                                var areaName = AppBasicCache.GetParkAreas.ContainsKey(areaNo) ? AppBasicCache.GetParkAreas[areaNo]?.ParkArea_Name : areaNo;
                                payAccountAreaCombinations.Add($"{pay.PayOrder_Account}|{areaName}");
                            }
                        }
                    }

                    // 从通行记录中获取操作员-区域组合（通过车道编码关联区域）
                    var passAccountAreaCombinations = new List<string>();
                    if (passRecordList != null)
                    {
                        foreach (var pass in passRecordList.Where(x => !string.IsNullOrEmpty(x.PassRecord_Account) && !string.IsNullOrEmpty(x.PassRecord_PasswayNo)))
                        {
                            // 通过车道编码找到对应的区域
                            var areaNo = groupedResult.FirstOrDefault(g => g.Value.Contains(pass.PassRecord_PasswayNo)).Key;
                            if (!string.IsNullOrEmpty(areaNo))
                            {
                                var areaName = AppBasicCache.GetParkAreas.ContainsKey(areaNo) ? AppBasicCache.GetParkAreas[areaNo]?.ParkArea_Name : areaNo;
                                passAccountAreaCombinations.Add($"{pass.PassRecord_Account}|{areaName}");
                            }
                        }
                    }

                    // 合并所有组合并去重
                    groupKeys = orderCombinations.Union(orderDetailCombinations).Union(payAccountAreaCombinations).Union(passAccountAreaCombinations).Distinct().ToList();

                    // 对于无法关联到区域的操作员，添加"未知区域"分组
                    var payAccounts2 = activePay?.Select(x => x.PayOrder_Account).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList() ?? new List<string>();
                    var passAccounts2 = passRecordList?.Select(x => x.PassRecord_Account).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList() ?? new List<string>();

                    foreach (var account in payAccounts2.Union(passAccounts2).Distinct())
                    {
                        if (!groupKeys.Any(x => x.StartsWith(account + "|")))
                        {
                            groupKeys.Add($"{account}|未知区域");
                        }
                    }
                    break;

                default:
                    // 默认按操作员分组
                    List<string> passAccountsDefault = passRecordList?.Select(x => x.PassRecord_Account).ToList() ?? new List<string>();
                    List<string> payAccountsDefault = activePay?.Select(x => x.PayOrder_Account).ToList() ?? new List<string>();
                    List<string> opAccountListDefault = orders?.Select(x => x.ParkOrder_OutAdminAccount).ToList() ?? new List<string>();
                    List<string> orderDetailAccountsDefault = orderdetails?.Select(x => x.OrderDetail_OutAdminAccount).ToList() ?? new List<string>();
                    opAccountListDefault?.AddRange(payAccountsDefault);
                    opAccountListDefault?.AddRange(passAccountsDefault);
                    opAccountListDefault?.AddRange(orderDetailAccountsDefault);
                    groupKeys = opAccountListDefault?.Distinct().Where(x => !string.IsNullOrEmpty(x)).ToList() ?? new List<string>();
                    break;
            }

            //入口车道
            List<string> passInList = new List<string>();
            List<string> passOutList = new List<string>();

            AppBasicCache.GetAllPassway.Values.ToList().ForEach(x =>
            {
                int gate = BLL.Passway.GetPasswayGateType(x.Passway_No);
                if (gate == 1 || gate == 2)
                {
                    passInList.Add(x.Passway_No);
                }
                else
                {
                    passOutList.Add(x.Passway_No);
                }
            });

            foreach (var groupKey in groupKeys)
            {
                Model.RptPrintItem item = new Model.RptPrintItem();

                switch (groupType)
                {
                    case 0: // 操作员
                        item = GetRptPrintItemByOperator(groupKey, normalOrderDetail, freeOrderDetail, normalPay, activePay, passRecordList, passOutList, passInList, orderdetails, month, free, store, temp);
                        break;

                    case 1: // 区域
                        item = GetRptPrintItemByArea(groupKey, normalOrderDetail, freeOrderDetail, normalPay, activePay, passRecordList, passOutList, passInList, orderdetails, payPartList, groupedResult, month, free, store, temp);
                        break;

                    case 2: // 操作员+区域
                        item = GetRptPrintItemByOperatorAndArea(groupKey, normalOrderDetail, freeOrderDetail, normalPay, activePay, passRecordList, passOutList, passInList, orderdetails, payPartList, groupedResult, month, free, store, temp);
                        break;

                    default:
                        // 默认按操作员处理
                        var defaultAccount = groupKey;
                        item.Account = defaultAccount;
                        item.OpenGateCount = passRecordList?.FindAll(x => x.PassRecord_Account == defaultAccount && passOutList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
                        item.OpenGateInCount = passRecordList?.FindAll(x => x.PassRecord_Account == defaultAccount && passInList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
                        // 使用orderdetails进行车辆数量统计（默认分组）
                        item.MonthCount = normalOrderDetail?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount && month.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
                        item.MonthMoney = normalPay?.FindAll(x => x.PayOrder_Account == defaultAccount && x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.MonthCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        item.FreeCount = normalOrderDetail?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount && free.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
                        item.FreeMoney = normalPay?.FindAll(x => x.PayOrder_Account == defaultAccount && free.Contains(x.PayOrder_CarCardTypeNo)).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        item.StoreCount = normalOrderDetail?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount && store.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
                        item.StoreMoney = normalPay?.FindAll(x => x.PayOrder_Account == defaultAccount && x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.StoreCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        item.TempCount = normalOrderDetail?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount && temp.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
                        item.TempMoney = normalPay?.FindAll(x => x.PayOrder_Account == defaultAccount && x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.Temp).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        item.FreeOutCount = freeOrderDetail?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount).Count ?? 0;
                        item.TotalCount = (orderdetails?.FindAll(x => x.OrderDetail_OutAdminAccount == defaultAccount).Count + passRecordList?.FindAll(x => x.PassRecord_Account == defaultAccount).Count) ?? 0;
                        item.TotalYsMoney = activePay?.FindAll(x => x.PayOrder_Account == defaultAccount).Sum(x => x.PayOrder_Money) ?? 0;
                        item.TotalJmMoney = activePay?.FindAll(x => x.PayOrder_Account == defaultAccount).Sum(x => x.PayOrder_DiscountMoney) ?? 0;
                        item.TotalSsMoney = activePay?.FindAll(x => x.PayOrder_Account == defaultAccount).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        break;
                }

                data.RptPrintList.Add(item);
            }

            var lstTotal = new Model.RptPrintItem();
            // 使用orderdetails进行总计车辆数量统计
            lstTotal.MonthCount = normalOrderDetail?.FindAll(x => month.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
            lstTotal.FreeCount = normalOrderDetail?.FindAll(x => free.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
            lstTotal.StoreCount = normalOrderDetail?.FindAll(x => store.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
            lstTotal.TempCount = normalOrderDetail?.FindAll(x => temp.Contains(x.OrderDetail_CarCardType)).Count ?? 0;
            lstTotal.FreeOutCount = freeOrderDetail?.Count ?? 0;
            lstTotal.OpenGateCount = passRecordList?.FindAll(x => passOutList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
            lstTotal.OpenGateInCount = passRecordList?.FindAll(x => passInList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
            lstTotal.TotalCount = (orderdetails?.Count + passRecordList?.Count) ?? 0;

            // 根据groupType决定支付金额统计数据源
            if ((groupType == 1 || groupType == 2) && payPartList != null && payPartList.Count > 0)
            {
                // 按区域或按操作员+区域分组时，使用payPartList进行支付金额统计
                lstTotal.MonthMoney = payPartList?.FindAll(x => x.PayPart_OrderTypeNo == ((int)Common.EnumOrderType.MonthCharge).ToString()).Sum(x => x.PayPart_PayedMoney) ?? 0;
                lstTotal.FreeMoney = payPartList?.FindAll(x => free.Contains(x.PayPart_CarCardTypeNo)).Sum(x => x.PayPart_PayedMoney) ?? 0;
                lstTotal.StoreMoney = payPartList?.FindAll(x => x.PayPart_OrderTypeNo == ((int)Common.EnumOrderType.StoreCharge).ToString()).Sum(x => x.PayPart_PayedMoney) ?? 0;
                lstTotal.TempMoney = payPartList?.FindAll(x => x.PayPart_OrderTypeNo == ((int)Common.EnumOrderType.Temp).ToString()).Sum(x => x.PayPart_PayedMoney) ?? 0;
                lstTotal.TotalYsMoney = payPartList?.Sum(x => x.PayPart_OrderMoney) ?? 0;
                lstTotal.TotalJmMoney = payPartList?.Sum(x => x.PayPart_CouponMoney) ?? 0;
                lstTotal.TotalSsMoney = payPartList?.Sum(x => x.PayPart_PayedMoney) ?? 0;
            }
            else
            {
                // 按操作员分组或payPartList为空时，使用PayOrder进行支付金额统计
                lstTotal.MonthMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.MonthCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                lstTotal.FreeMoney = normalPay?.FindAll(x => free.Contains(x.PayOrder_CarCardTypeNo)).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                lstTotal.StoreMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.StoreCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                lstTotal.TempMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.Temp).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                lstTotal.TotalYsMoney = activePay?.Sum(x => x.PayOrder_Money) ?? 0;
                lstTotal.TotalJmMoney = activePay?.Sum(x => x.PayOrder_DiscountMoney) ?? 0;
                lstTotal.TotalSsMoney = activePay?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
            }
            data.RptPrintListTotal = lstTotal;

            Model.RptPrintOnline model = new Model.RptPrintOnline();
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CMBNonInductive.ToString())?.PayType_Enable == 1)
                model.ZhaoHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CMBNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.NetcomPayment.ToString())?.PayType_Enable == 1)
                model.ZhaoHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.NetcomPayment.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ICBCNonInductive.ToString())?.PayType_Enable == 1)
                model.GongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ICBCNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ICBCInductive.ToString())?.PayType_Enable == 1)
                model.GongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ICBCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ABCInductive.ToString())?.PayType_Enable == 1)
                model.NongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ABCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ABCNonInductive.ToString())?.PayType_Enable == 1)
                model.NongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ABCNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.SwiftPass.ToString())?.PayType_Enable == 1)
                model.WeiFuTongAgg = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.SwiftPass.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OtherPay.ToString())?.PayType_Enable == 1)
                model.ThreeAgg = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OtherPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CCBNonInductive.ToString())?.PayType_Enable == 1)
                model.JianHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CCBNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CCBInductive.ToString())?.PayType_Enable == 1)
                model.JianHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CCBInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.WeixinNonInductive.ToString())?.PayType_Enable == 1)
                model.WeiXinAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.WeixinNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.WeiChat.ToString())?.PayType_Enable == 1)
                model.WeiXinOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.WeiChat.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OffLineWxPay.ToString())?.PayType_Enable == 1)
                model.WeiXin = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineWxPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.UparkInductive.ToString())?.PayType_Enable == 1)
                model.YinLianAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.UparkInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.unionPayBusinessPayments.ToString())?.PayType_Enable == 1)
                model.YinLian = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.unionPayBusinessPayments.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.AlipayNonInductive.ToString())?.PayType_Enable == 1)
                model.AliPayAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.AlipayNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.AliPay.ToString())?.PayType_Enable == 1)
                model.AliPayOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.AliPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OffLineAlipay.ToString())?.PayType_Enable == 1)
                model.AliPay = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineAlipay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.Platform.ToString())?.PayType_Enable == 1)
                model.CashOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.noFeelingBOC.ToString())?.PayType_Enable == 1)
                model.ZhongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.noFeelingBOC.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.BOCPay.ToString())?.PayType_Enable == 1)
                model.ZhongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.BOCPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.leConvergencePayment.ToString())?.PayType_Enable == 1)
                model.LeShua = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.leConvergencePayment.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.owinDeviceCashMoney.ToString())?.PayType_Enable == 1)
                model.ZhiZhuCash = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.owinDeviceCashMoney.ToString()).Sum(x => x.PayOrder_SelfMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ETCInductive.ToString())?.PayType_Enable == 1)
                model.ETC = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ETCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.SxPay.ToString())?.PayType_Enable == 1)
                model.SxPay = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.SxPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;

            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.owinDeviceCashMoney.ToString())?.PayType_Enable == 1)
                model.ZhaoLing = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.owinDeviceCashMoney.ToString()).Sum(x => x.PayOrder_OutReduceMoney).Value;
            model.Cash = payOrderCashList.Sum(x => x.PayOrder_PayedMoney).Value;

            model.OnlineYingShou = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() && x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_Money) ?? 0;
            model.OnlineJianMian = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() && x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_DiscountMoney) ?? 0;

            model.CashYingShou = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_Money) ?? 0;
            model.CashJianMian = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_DiscountMoney) ?? 0;

            data.onlineData = model;
        }

        /// <summary>
        /// 获取月报打印数据
        /// </summary>
        /// <param name="groupType">分组类型:0-操作员,1-区域,2-操作员+区域</param>
        public static void GetRptMonthPrintData(string parkno, DateTime start, DateTime end, out Model.RptPrintData data, int dataType = 0, int groupType = 0)
        {
            data = new Model.RptPrintData()
            {
                onlineData = new Model.RptPrintOnline(),
                RptPrintList = new List<Model.RptPrintItem>(),
                RptPrintListTotal = new Model.RptPrintItem()
            };

            List<Model.PayType> pts = BLL.BaseBLL._GetAllEntity(new Model.PayType(), "*", $"PayType_Enable=1");

            List<string> monthGroup = new List<string>();
            DateTime ssdt = DateTime.Parse(start.ToString("yyyy-MM-dd HH:mm:ss"));
            while (ssdt.Date <= end.Date)
            {
                monthGroup.Add(ssdt.ToString("yyyy-MM-dd"));
                ssdt = ssdt.AddDays(1);
            }
            if (monthGroup.Count == 0) return;

            List<string> orderFileds = new List<string>()
            {
                "ParkOrder_No",
                "ParkOrder_CarCardType",
                "ParkOrder_CarType",
                "ParkOrder_StatusNo",
                "ParkOrder_IsFree",
                "ParkOrder_IsNoInRecord",
                "ParkOrder_CarCardType",
                "ParkOrder_OutAdminAccount",
                "ParkOrder_OutTime",
                "ParkOrder_ParkAreaNo",
                "ParkOrder_ParkAreaName"
            };

            StringBuilder orderSql = new StringBuilder();
            orderSql.Append($" ParkOrder_ParkNo='{parkno}' ");
            orderSql.Append($" AND ParkOrder_StatusNo=201 ");
            orderSql.Append($" AND ParkOrder_OutTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND ParkOrder_OutTime < '{end.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity(string.Join(",", orderFileds), orderSql.ToString(), dataType, start);

            var normalOrder = orders?.FindAll(x => x.ParkOrder_IsFree == 0);
            var freeOrder = orders?.FindAll(x => x.ParkOrder_IsFree == 1);
            var freeOrderNo = freeOrder?.Select(x => x.ParkOrder_No).ToList() ?? new List<string>();

            StringBuilder passRWhere = new StringBuilder();
            passRWhere.Append($" PassRecord_ParkNo='{parkno}' ");
            passRWhere.Append($" AND (PassRecord_Type=1 OR PassRecord_Type=2) ");
            passRWhere.Append($" AND PassRecord_PassTime  >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND PassRecord_PassTime< '{end.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.PassRecord> passRecordList = BLL.PassRecord.GetAllEntity("PassRecord_PasswayNo,PassRecord_Type,PassRecord_Account,PassRecord_PassTime", passRWhere.ToString(), null) ?? new List<Model.PassRecord>();

            List<string> payFileds = new List<string>()
            {
                "PayOrder_OrderTypeNo",
                "PayOrder_Status",
                "PayOrder_PayedTime",
                "PayOrder_PayTypeCode",
                "PayOrder_Account",
                "PayOrder_Category",
                "PayOrder_CarCardTypeNo",
                "PayOrder_Money",
                "PayOrder_PayedMoney",
                "PayOrder_StoredMoney",
                "PayOrder_ParkOrderNo",
                "PayOrder_DiscountMoney",
                "PayOrder_PayedTime",
                "PayOrder_SelfMoney",
                "PayOrder_OutReduceMoney",
                "PayOrder_PassWayNo"
            };

            StringBuilder paySql = new StringBuilder();
            paySql.Append($" PayOrder_ParkNo='{parkno}' ");
            paySql.Append($" AND PayOrder_Status=1 ");
            paySql.Append($" AND (PayOrder_OrderTypeNo=5901 OR PayOrder_OrderTypeNo=5905 OR PayOrder_OrderTypeNo=5919)");//查询临时车缴费/月租车缴费/储值车缴费
            paySql.Append($" AND PayOrder_PayedTime >= '{start.ToString("yyyy-MM-dd HH:mm:ss")}' AND PayOrder_PayedTime< '{end.AddSeconds(1).ToString("yyyy-MM-dd 23:59:59")}' ");
            List<Model.PayOrder> payOrderList = BLL.PayOrder.GetAllEntity(string.Join(",", payFileds), paySql.ToString(), dataType, start);

            //&& x.PayOrder_PayTypeCode != Model.EnumPayType.owinDeviceCashMoney.ToString()
            var activePay = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineCash.ToString() && !string.IsNullOrWhiteSpace(x.PayOrder_Account));//操作员不为空,不是自助缴费
            var normalPay = activePay?.FindAll(x => !freeOrderNo.Contains(x.PayOrder_ParkOrderNo));//不包含免费放行的支付订单
            var payOrderCashList = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineCash.ToString() && !string.IsNullOrWhiteSpace(x.PayOrder_Account));//线下现金支付订单

            BLL.CarCardType.GetAllCardNoByType(parkno, out var temp, out var month, out var store, out var free, out var visit, out var bus);
            //访客车&商家车合并统计在临时车列
            if (visit != null) temp?.AddRange(visit);
            if (bus != null) temp?.AddRange(bus);

            //入口车道
            List<string> passInList = new List<string>();
            List<string> passOutList = new List<string>();

            AppBasicCache.GetAllPassway.Values.ToList().ForEach(x =>
            {
                int gate = BLL.Passway.GetPasswayGateType(x.Passway_No);
                if (gate == 1 || gate == 2)
                {
                    passInList.Add(x.Passway_No);
                }
                else
                {
                    passOutList.Add(x.Passway_No);
                }
            });

            foreach (var dstr in monthGroup)
            {
                Model.RptPrintItem item = new Model.RptPrintItem();
                var ds = normalOrder?.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd") == dstr);
                var ps = normalPay?.FindAll(x => x.PayOrder_PayedTime != null && x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == dstr);

                item.Account = dstr;
                item.MonthCount = ds?.FindAll(x => month.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
                item.MonthMoney = ps?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.MonthCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                item.FreeCount = ds?.FindAll(x => free.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
                item.FreeMoney = ps?.FindAll(x => free.Contains(x.PayOrder_CarCardTypeNo)).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                item.StoreCount = ds?.FindAll(x => store.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
                item.StoreMoney = ps?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.StoreCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                item.TempCount = ds?.FindAll(x => temp.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
                item.TempMoney = ps?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.Temp).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
                item.FreeOutCount = freeOrder?.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd") == dstr && x.ParkOrder_IsFree == 1).Count ?? 0;
                item.OpenGateCount = passRecordList?.FindAll(x => x.PassRecord_PassTime != null && x.PassRecord_PassTime.Value.ToString("yyyy-MM-dd") == dstr && passOutList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
                item.OpenGateInCount = passRecordList?.FindAll(x => x.PassRecord_PassTime != null && x.PassRecord_PassTime.Value.ToString("yyyy-MM-dd") == dstr && passInList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
                item.TotalCount = (orders?.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd") == dstr).Count + passRecordList?.FindAll(x => x.PassRecord_PassTime != null && x.PassRecord_PassTime.Value.ToString("yyyy-MM-dd") == dstr).Count) ?? 0;

                item.TotalYsMoney = activePay?.FindAll(x => x.PayOrder_PayedTime != null && x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == dstr).Sum(x => x.PayOrder_Money) ?? 0;
                item.TotalJmMoney = activePay?.FindAll(x => x.PayOrder_PayedTime != null && x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == dstr).Sum(x => x.PayOrder_DiscountMoney) ?? 0;
                item.TotalSsMoney = activePay?.FindAll(x => x.PayOrder_PayedTime != null && x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == dstr).Sum(x => x.PayOrder_PayedMoney) ?? 0;

                item.onlineMoney = payOrderList?.FindAll(x =>
                    x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() &&
                    x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString() &&
                    x.PayOrder_PayTypeCode != Model.EnumPayType.owinDeviceCashMoney.ToString() &&
                    //!freeOrderNo.Contains(x.PayOrder_ParkOrderNo) &&
                    x.PayOrder_PayedTime != null &&
                    x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == dstr).Sum(x => x.PayOrder_PayedMoney) ?? 0;

                data.RptPrintList.Add(item);
            }

            var lstTotal = new Model.RptPrintItem();
            lstTotal.MonthCount = normalOrder?.FindAll(x => month.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
            lstTotal.MonthMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.MonthCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
            lstTotal.FreeCount = normalOrder?.FindAll(x => free.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
            lstTotal.FreeMoney = normalPay?.FindAll(x => free.Contains(x.PayOrder_CarCardTypeNo)).Sum(x => x.PayOrder_PayedMoney) ?? 0;
            lstTotal.StoreCount = normalOrder?.FindAll(x => store.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
            lstTotal.StoreMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.StoreCharge).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
            lstTotal.TempCount = normalOrder?.FindAll(x => temp.Contains(x.ParkOrder_CarCardType)).Count ?? 0;
            lstTotal.TempMoney = normalPay?.FindAll(x => x.PayOrder_OrderTypeNo == ((int)Common.EnumOrderType.Temp).ToString()).Sum(x => x.PayOrder_PayedMoney) ?? 0;
            lstTotal.FreeOutCount = orders?.FindAll(x => x.ParkOrder_IsFree == 1).Count ?? 0;
            lstTotal.OpenGateCount = passRecordList?.FindAll(x => passOutList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
            lstTotal.OpenGateInCount = passRecordList?.FindAll(x => passInList.Contains(x.PassRecord_PasswayNo)).Count ?? 0;
            lstTotal.TotalCount = (orders?.Count + passRecordList?.Count) ?? 0;
            lstTotal.TotalYsMoney = activePay?.Sum(x => x.PayOrder_Money) ?? 0;
            lstTotal.TotalJmMoney = activePay?.Sum(x => x.PayOrder_DiscountMoney) ?? 0;
            lstTotal.TotalSsMoney = activePay?.Sum(x => x.PayOrder_PayedMoney) ?? 0;

            lstTotal.onlineMoney = payOrderList?.FindAll(x =>
                    x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() &&
                    x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString() &&
                    x.PayOrder_PayTypeCode != Model.EnumPayType.owinDeviceCashMoney.ToString()
                    //&&!freeOrderNo.Contains(x.PayOrder_ParkOrderNo)
                    ).Sum(x => x.PayOrder_PayedMoney) ?? 0;

            data.RptPrintListTotal = lstTotal;

            Model.RptPrintOnline model = new Model.RptPrintOnline();
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CMBNonInductive.ToString())?.PayType_Enable == 1)
                model.ZhaoHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CMBNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.NetcomPayment.ToString())?.PayType_Enable == 1)
                model.ZhaoHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.NetcomPayment.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ICBCNonInductive.ToString())?.PayType_Enable == 1)
                model.GongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ICBCNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ICBCInductive.ToString())?.PayType_Enable == 1)
                model.GongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ICBCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ABCInductive.ToString())?.PayType_Enable == 1)
                model.NongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ABCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ABCNonInductive.ToString())?.PayType_Enable == 1)
                model.NongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ABCNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.SwiftPass.ToString())?.PayType_Enable == 1)
                model.WeiFuTongAgg = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.SwiftPass.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OtherPay.ToString())?.PayType_Enable == 1)
                model.ThreeAgg = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OtherPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CCBNonInductive.ToString())?.PayType_Enable == 1)
                model.JianHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CCBNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.CCBInductive.ToString())?.PayType_Enable == 1)
                model.JianHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.CCBInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.WeixinNonInductive.ToString())?.PayType_Enable == 1)
                model.WeiXinAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.WeixinNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.WeiChat.ToString())?.PayType_Enable == 1)
                model.WeiXinOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.WeiChat.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OffLineWxPay.ToString())?.PayType_Enable == 1)
                model.WeiXin = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineWxPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.UparkInductive.ToString())?.PayType_Enable == 1)
                model.YinLianAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.UparkInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.unionPayBusinessPayments.ToString())?.PayType_Enable == 1)
                model.YinLian = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.unionPayBusinessPayments.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.AlipayNonInductive.ToString())?.PayType_Enable == 1)
                model.AliPayAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.AlipayNonInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.AliPay.ToString())?.PayType_Enable == 1)
                model.AliPayOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.AliPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.OffLineAlipay.ToString())?.PayType_Enable == 1)
                model.AliPay = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.OffLineAlipay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.Platform.ToString())?.PayType_Enable == 1)
                model.CashOnline = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.noFeelingBOC.ToString())?.PayType_Enable == 1)
                model.ZhongHangAuto = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.noFeelingBOC.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.BOCPay.ToString())?.PayType_Enable == 1)
                model.ZhongHang = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.BOCPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.leConvergencePayment.ToString())?.PayType_Enable == 1)
                model.LeShua = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.leConvergencePayment.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.owinDeviceCashMoney.ToString())?.PayType_Enable == 1)
                model.ZhiZhuCash = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.owinDeviceCashMoney.ToString()).Sum(x => x.PayOrder_SelfMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.ETCInductive.ToString())?.PayType_Enable == 1)
                model.ETC = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.ETCInductive.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;
            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.SxPay.ToString())?.PayType_Enable == 1)
                model.SxPay = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.SxPay.ToString()).Sum(x => x.PayOrder_PayedMoney).Value;

            if (pts?.Find(s => s.PayType_No == Model.EnumPayType.owinDeviceCashMoney.ToString())?.PayType_Enable == 1)
                model.ZhaoLing = payOrderList.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.owinDeviceCashMoney.ToString()).Sum(x => x.PayOrder_OutReduceMoney).Value;
            model.Cash = payOrderCashList.Sum(x => x.PayOrder_PayedMoney).Value;

            model.OnlineYingShou = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() && x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString())?.Sum(x => x.PayOrder_Money) ?? 0;
            model.OnlineJianMian = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode != Model.EnumPayType.OffLineCash.ToString() && x.PayOrder_PayTypeCode != Model.EnumPayType.Platform.ToString())?.Sum(x => x.PayOrder_DiscountMoney) ?? 0;

            model.CashYingShou = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString())?.Sum(x => x.PayOrder_Money) ?? 0;
            model.CashJianMian = payOrderList?.FindAll(x => x.PayOrder_PayTypeCode == Model.EnumPayType.Platform.ToString())?.Sum(x => x.PayOrder_DiscountMoney) ?? 0;

            data.onlineData = model;
        }

        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="type">文件类型:1-日报表,2-月报表,3-车流量统计报表,4-临时车收费统计报表,5-月租车充值统计,6-储值车充值统计,7-车位续费统计</param>
        /// <param name="groupType">分组类型:0-操作员,1-区域,2-操作员+区域</param>
        /// <returns></returns>
        public static T GetRptData<T>(DateTime start, DateTime end, int type, out bool old, out DateTime? histime, int dataType = 0, int groupType = 0)
        {
            old = CheckHistroyData(start, end, type, out var filePath, out histime, dataType, false, groupType);
            var data = GetRptAnalysis<T>(type, start, end, filePath, old, dataType, groupType);
            if (old && data == null)
            {
                return GetRptAnalysis<T>(type, start, end, null, false, dataType, groupType);
            }
            return data;
        }

        /// <summary>
        /// 判断是否有历史报表数据
        /// </summary>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="type"></param>
        /// <param name="filePath"></param>
        /// <param name="groupType">统计方式:0-操作员,1-区域,2-操作员+区域</param>
        /// <returns></returns>
        public static bool CheckHistroyData(DateTime start, DateTime end, int type, out string filePath, out DateTime? histime, int dataType = 0, bool isPrintExport = false, int groupType = 0)
        {
            histime = null;
            var old = true;
            var hisData = BLL.BaseBLL._GetEntityByWhere(new Model.HistoryReport(), "HistoryReport_FilePath,HistoryReport_AddTime", $"HistoryReport_BeginTime='{start.ToString("yyyy-MM-dd HH:mm:ss")}' and HistoryReport_EndTime='{end.ToString("yyyy-MM-dd HH:mm:ss")}' and HistoryReport_Type='{(dataType == 1 ? type + 1000 : type)}' and HistoryReport_GroupType='{groupType}' order by HistoryReport_ID limit 1");
            if (hisData != null)
            {
                histime = hisData.HistoryReport_AddTime;
                //其它报表
                if (type != 1 && !isPrintExport)
                {
                    //有历史
                    if (hisData.HistoryReport_AddTime >= DateTime.Parse(end.ToString("yyyy-MM-dd 23:59:59")))
                    {
                        old = true;
                    }
                    //无历史
                    else
                    {
                        old = false;
                    }
                }
                //日报表
                else
                {
                    old = true;
                }
            }
            //无历史
            else
            {
                old = false;
            }

            filePath = hisData?.HistoryReport_FilePath;

            //存在历史,但是文件不存在
            if (old)
            {
                if (!FileHelper.CheckFile(filePath)) return false;
            }

            return old;
        }

        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="type"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="filePath"></param>
        /// <param name="old"></param>
        /// <param name="groupType">分组类型:0-操作员,1-区域,2-操作员+区域</param>
        /// <returns></returns>
        public static T GetRptAnalysis<T>(int type, DateTime? start = null, DateTime? end = null, string filePath = "", bool old = true, int dataType = 0, int groupType = 0)
        {
            if (old) return FileHelper.LoadFile<T>(filePath);
            if (!old && !string.IsNullOrEmpty(filePath)) FileHelper.DeleteFile(filePath);

            T t = default(T);
            var no = Utils.CreateNumber;
            filePath = Path.GetDirectoryName(BLL.ImageTools.LocalFilePath);
            filePath = Path.Combine(filePath, "Report", DateTime.Now.ToString("yyyyMM"));

            switch (type)
            {
                case 1:
                    BLL.RptAnalysis.GetRptPrintData(AppBasicCache.GetParking?.Parking_No, start.Value, end.Value, out var data, dataType, groupType);
                    t = (T)(object)data;
                    no = "DB" + no;
                    break;
                case 2:
                    BLL.RptAnalysis.GetRptMonthPrintData(AppBasicCache.GetParking?.Parking_No, start.Value, end.Value, out var d, dataType, groupType);
                    t = (T)(object)d;
                    no = "MB" + no;
                    break;
                case 3:
                    var clData = BLL.RptAnalysis.TrafficRptAnalysis(start, end, dataType);
                    clData?.ForEach(x =>
                    {
                        x.orders = null;
                    });
                    t = (T)(object)clData;
                    no = "CL" + no;
                    break;
                case 4:
                    var lsData = BLL.RptAnalysis.TempRptAnalysis(start, end, dataType);
                    lsData?.ForEach(x =>
                    {
                        x.orders = null;
                    });
                    t = (T)(object)lsData;
                    no = "LS" + no;
                    break;
                case 5:
                    var yzData = BLL.RptAnalysis.MonthRptAnalysis(start, end, dataType);
                    yzData?.ForEach(x =>
                    {
                        x.orders = null;
                    });
                    t = (T)(object)yzData;
                    no = "YZ" + no;
                    break;
                case 6:
                    var czData = BLL.RptAnalysis.CarStoreAnalysis(start, end, dataType);
                    czData?.ForEach(x =>
                    {
                        x.orders = null;
                    });
                    t = (T)(object)czData;
                    no = "CZ" + no;
                    break;
                case 7:
                    var cwData = BLL.RptAnalysis.SpaceRptAnalysis(start, end, dataType);
                    cwData?.ForEach(x =>
                    {
                        x.orders = null;
                    });
                    t = (T)(object)cwData;
                    no = "CW" + no;
                    break;
            }

            //保存历史报表
            var file = Path.Combine(filePath, no + ".json");
            BLL.CommonBLL.SaveHisReport<T>(no, file, start.Value, end.Value, t, type, dataType, groupType);
            return t;
        }
    }
}
